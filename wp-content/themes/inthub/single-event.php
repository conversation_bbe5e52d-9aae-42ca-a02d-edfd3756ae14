<?php
/**
 * The template for displaying single events
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container mx-auto px-4 py-16">
        <?php while (have_posts()) : the_post(); ?>
            <?php
            // Get event meta data
            $start_datetime = get_post_meta(get_the_ID(), 'event_start_datetime', true);
            $end_datetime = get_post_meta(get_the_ID(), 'event_end_datetime', true);
            $location = get_post_meta(get_the_ID(), 'event_location', true);
            $short_description = get_post_meta(get_the_ID(), 'event_short_description', true);
            
            // Format dates for display
            $start_date_formatted = $start_datetime ? date('d/m/Y', strtotime($start_datetime)) : '';
            $start_time_formatted = $start_datetime ? date('H:i', strtotime($start_datetime)) : '';
            $end_date_formatted = $end_datetime ? date('d/m/Y', strtotime($end_datetime)) : '';
            $end_time_formatted = $end_datetime ? date('H:i', strtotime($end_datetime)) : '';
            
            // Determine event status
            $now = current_time('timestamp');
            $start_time = $start_datetime ? strtotime($start_datetime) : 0;
            $end_time = $end_datetime ? strtotime($end_datetime) : 0;
            
            $event_status = '';
            $status_class = '';
            if ($start_time && $end_time) {
                if ($now < $start_time) {
                    $event_status = 'Sắp diễn ra';
                    $status_class = 'bg-blue-100 text-blue-800';
                } elseif ($now >= $start_time && $now <= $end_time) {
                    $event_status = 'Đang diễn ra';
                    $status_class = 'bg-green-100 text-green-800';
                } else {
                    $event_status = 'Đã kết thúc';
                    $status_class = 'bg-gray-100 text-gray-800';
                }
            }
            ?>
            
            <article id="post-<?php the_ID(); ?>" <?php post_class('max-w-4xl mx-auto'); ?> itemscope itemtype="https://schema.org/Event">
                
                <!-- Event Header -->
                <header class="entry-header text-center mb-12">
                    <h1 class="entry-title text-3xl md:text-4xl font-bold text-blue-900 mb-6" itemprop="name">
                        <?php the_title(); ?>
                    </h1>
                    
                    <?php if ($event_status) : ?>
                        <div class="mb-6">
                            <span class="inline-block px-4 py-2 rounded-full text-sm font-semibold <?php echo $status_class; ?>">
                                <?php echo $event_status; ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($short_description) : ?>
                        <div class="text-lg text-gray-600 max-w-2xl mx-auto mb-8" itemprop="description">
                            <?php echo esc_html($short_description); ?>
                        </div>
                    <?php endif; ?>
                </header>

                <!-- Event Details Card -->
                <div class="bg-gradient-to-r from-blue-50 to-pink-50 rounded-lg p-8 mb-12 border border-gray-200">
                    <h2 class="text-2xl font-bold text-blue-900 mb-6 text-center">
                        <?php esc_html_e('Thông tin sự kiện', 'inthub'); ?>
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Start Date & Time -->
                        <?php if ($start_datetime) : ?>
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-12 h-12 bg-pink-500 text-white rounded-lg flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-blue-900 mb-1"><?php esc_html_e('Bắt đầu', 'inthub'); ?></h3>
                                    <div class="text-gray-700" itemprop="startDate" content="<?php echo esc_attr($start_datetime); ?>">
                                        <div class="font-medium"><?php echo $start_date_formatted; ?></div>
                                        <div class="text-sm"><?php echo $start_time_formatted; ?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- End Date & Time -->
                        <?php if ($end_datetime) : ?>
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-12 h-12 bg-blue-500 text-white rounded-lg flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-blue-900 mb-1"><?php esc_html_e('Kết thúc', 'inthub'); ?></h3>
                                    <div class="text-gray-700" itemprop="endDate" content="<?php echo esc_attr($end_datetime); ?>">
                                        <div class="font-medium"><?php echo $end_date_formatted; ?></div>
                                        <div class="text-sm"><?php echo $end_time_formatted; ?></div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Location -->
                        <?php if ($location) : ?>
                            <div class="flex items-start space-x-4 md:col-span-2">
                                <div class="flex-shrink-0 w-12 h-12 bg-green-500 text-white rounded-lg flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-blue-900 mb-1"><?php esc_html_e('Địa điểm', 'inthub'); ?></h3>
                                    <div class="text-gray-700" itemprop="location" itemscope itemtype="https://schema.org/Place">
                                        <span itemprop="address"><?php echo esc_html($location); ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Featured Image -->
                <?php if (has_post_thumbnail()) : ?>
                    <div class="entry-featured-image mb-12">
                        <?php the_post_thumbnail('large', array('class' => 'w-full h-auto rounded-lg shadow-lg', 'itemprop' => 'image')); ?>
                    </div>
                <?php endif; ?>

                <!-- Event Content -->
                <div class="entry-content prose prose-lg max-w-none mb-12" itemprop="description">
                    <?php
                    the_content(sprintf(
                        wp_kses(
                            /* translators: %s: Name of current post. Only visible to screen readers */
                            __('Continue reading<span class="screen-reader-text"> "%s"</span>', 'inthub'),
                            array(
                                'span' => array(
                                    'class' => array(),
                                ),
                            )
                        ),
                        wp_kses_post(get_the_title())
                    ));

                    wp_link_pages(array(
                        'before' => '<div class="page-links">' . esc_html__('Pages:', 'inthub'),
                        'after'  => '</div>',
                    ));
                    ?>
                </div>

                <!-- Action Buttons -->
                <div class="text-center mb-12">
                    <div class="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4">
                        <?php if ($location) : ?>
                            <a href="https://www.google.com/maps/search/?api=1&query=<?php echo urlencode($location); ?>" 
                               target="_blank" 
                               rel="noopener noreferrer"
                               class="inline-flex items-center justify-center bg-green-500 text-white font-semibold py-3 px-6 rounded-lg hover:bg-green-600 transition duration-300">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <?php esc_html_e('Xem bản đồ', 'inthub'); ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($start_datetime) : ?>
                            <button onclick="addToCalendar()" 
                                    class="inline-flex items-center justify-center bg-blue-500 text-white font-semibold py-3 px-6 rounded-lg hover:bg-blue-600 transition duration-300">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <?php esc_html_e('Thêm vào lịch', 'inthub'); ?>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Social Share -->
                <div class="text-center mb-12">
                    <h3 class="text-lg font-semibold text-blue-900 mb-4">
                        <?php esc_html_e('Chia sẻ sự kiện:', 'inthub'); ?>
                    </h3>
                    <div class="flex justify-center space-x-4">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" target="_blank" rel="noopener noreferrer" class="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition duration-300" aria-label="<?php esc_attr_e('Share on Facebook', 'inthub'); ?>">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path>
                            </svg>
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" target="_blank" rel="noopener noreferrer" class="w-12 h-12 bg-blue-400 text-white rounded-full flex items-center justify-center hover:bg-blue-500 transition duration-300" aria-label="<?php esc_attr_e('Share on Twitter', 'inthub'); ?>">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path>
                            </svg>
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(get_permalink()); ?>" target="_blank" rel="noopener noreferrer" class="w-12 h-12 bg-blue-700 text-white rounded-full flex items-center justify-center hover:bg-blue-800 transition duration-300" aria-label="<?php esc_attr_e('Share on LinkedIn', 'inthub'); ?>">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </article>

            <!-- Event Navigation -->
            <nav class="event-navigation mt-16 pt-8 border-t border-gray-200">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <?php
                    $prev_event = get_previous_post(false, '', 'event');
                    $next_event = get_next_post(false, '', 'event');
                    ?>
                    
                    <?php if ($prev_event) : ?>
                        <div class="nav-previous">
                            <a href="<?php echo esc_url(get_permalink($prev_event)); ?>" class="flex items-center text-blue-900 hover:text-pink-500 transition duration-300">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                                <div>
                                    <div class="text-sm text-gray-600"><?php esc_html_e('Sự kiện trước', 'inthub'); ?></div>
                                    <div class="font-medium"><?php echo esc_html(get_the_title($prev_event)); ?></div>
                                </div>
                            </a>
                        </div>
                    <?php endif; ?>
                    
                    <div class="text-center">
                        <a href="<?php echo esc_url(get_post_type_archive_link('event')); ?>" class="inline-flex items-center justify-center bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-lg hover:bg-gray-200 transition duration-300">
                            <?php esc_html_e('Tất cả sự kiện', 'inthub'); ?>
                        </a>
                    </div>
                    
                    <?php if ($next_event) : ?>
                        <div class="nav-next">
                            <a href="<?php echo esc_url(get_permalink($next_event)); ?>" class="flex items-center text-blue-900 hover:text-pink-500 transition duration-300">
                                <div class="text-right">
                                    <div class="text-sm text-gray-600"><?php esc_html_e('Sự kiện tiếp theo', 'inthub'); ?></div>
                                    <div class="font-medium"><?php echo esc_html(get_the_title($next_event)); ?></div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </nav>

        <?php endwhile; ?>
    </div>
</main>

<script>
function addToCalendar() {
    <?php if ($start_datetime && $end_datetime) : ?>
        const startDate = new Date('<?php echo esc_js($start_datetime); ?>');
        const endDate = new Date('<?php echo esc_js($end_datetime); ?>');
        const title = '<?php echo esc_js(get_the_title()); ?>';
        const location = '<?php echo esc_js($location); ?>';
        const description = '<?php echo esc_js($short_description); ?>';
        
        // Format dates for Google Calendar
        const formatDate = (date) => {
            return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
        };
        
        const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(title)}&dates=${formatDate(startDate)}/${formatDate(endDate)}&location=${encodeURIComponent(location)}&details=${encodeURIComponent(description)}`;
        
        window.open(googleCalendarUrl, '_blank');
    <?php endif; ?>
}
</script>

<?php
get_footer();
?>
