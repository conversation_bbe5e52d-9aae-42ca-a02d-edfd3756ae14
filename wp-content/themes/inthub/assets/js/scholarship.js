/**
 * Scholarship JavaScript
 *
 * @package IntHub
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Scholarship Archive functionality
    const ScholarshipArchive = {
        init: function() {
            this.bindEvents();
            this.updateResultsCount();
            this.handleURLParams();
        },

        bindEvents: function() {
            // Filter events
            $('#university-filter').on('change', this.filterScholarships.bind(this));
            $('#country-filter').on('change', this.filterScholarships.bind(this));
            $('#value-filter').on('change', this.filterScholarships.bind(this));
            $('#search-filter').on('input', this.debounce(this.filterScholarships.bind(this), 300));
            
            // Reset filters
            $(document).on('click', '.reset-filters', this.resetFilters.bind(this));
            
            // Load more functionality
            $(document).on('click', '.load-more-scholarships', this.loadMore.bind(this));
        },

        handleURLParams: function() {
            // Handle university parameter from URL
            const urlParams = new URLSearchParams(window.location.search);
            const universityId = urlParams.get('university');
            
            if (universityId) {
                $('#university-filter').val(universityId);
                this.filterScholarships();
            }
        },

        filterScholarships: function() {
            const universityFilter = $('#university-filter').val();
            const countryFilter = $('#country-filter').val().toLowerCase();
            const valueFilter = $('#value-filter').val().toLowerCase();
            const searchFilter = $('#search-filter').val().toLowerCase();
            
            let visibleCount = 0;
            
            $('.scholarship-card').each(function() {
                const $card = $(this);
                const university = $card.data('university') || '';
                const country = $card.data('country') || '';
                const value = $card.data('value') || '';
                const name = $card.data('name') || '';
                
                let showCard = true;
                
                // University filter
                if (universityFilter && university != universityFilter) {
                    showCard = false;
                }
                
                // Country filter
                if (countryFilter && country !== countryFilter) {
                    showCard = false;
                }
                
                // Value filter
                if (valueFilter && value !== valueFilter) {
                    showCard = false;
                }
                
                // Search filter
                if (searchFilter && name.indexOf(searchFilter) === -1) {
                    showCard = false;
                }
                
                if (showCard) {
                    $card.removeClass('hidden').fadeIn(300);
                    visibleCount++;
                } else {
                    $card.addClass('hidden').fadeOut(300);
                }
            });
            
            this.updateResultsCount(visibleCount);
            this.showNoResults(visibleCount === 0);
        },

        updateResultsCount: function(count) {
            if (typeof count === 'undefined') {
                count = $('.scholarship-card:not(.hidden)').length;
            }
            $('#results-count').text(count);
        },

        showNoResults: function(show) {
            if (show) {
                if ($('.no-results').length === 0) {
                    const noResultsHtml = `
                        <div class="no-results col-span-full text-center py-12">
                            <div class="text-gray-500">
                                <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                <h3 class="text-xl font-medium mb-2">Không tìm thấy học bổng nào</h3>
                                <p>Vui lòng thử lại với từ khóa khác hoặc điều chỉnh bộ lọc.</p>
                                <button class="reset-filters mt-4 inline-flex items-center text-pink-600 hover:text-pink-800">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Đặt lại bộ lọc
                                </button>
                            </div>
                        </div>
                    `;
                    $('#scholarships-grid').append(noResultsHtml);
                }
            } else {
                $('.no-results').remove();
            }
        },

        resetFilters: function() {
            $('#university-filter').val('');
            $('#country-filter').val('');
            $('#value-filter').val('');
            $('#search-filter').val('');
            $('.scholarship-card').removeClass('hidden').fadeIn(300);
            this.updateResultsCount();
            this.showNoResults(false);
            
            // Update URL
            if (window.history && window.history.pushState) {
                window.history.pushState({}, '', window.location.pathname);
            }
        },

        loadMore: function(e) {
            e.preventDefault();
            const $button = $(e.currentTarget);
            const page = $button.data('page') || 2;
            
            $button.addClass('loading').text('Đang tải...');
            
            $.ajax({
                url: inthub_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'load_more_scholarships',
                    page: page,
                    nonce: inthub_ajax.nonce,
                    university: $('#university-filter').val(),
                    country: $('#country-filter').val(),
                    value: $('#value-filter').val(),
                    search: $('#search-filter').val()
                },
                success: function(response) {
                    if (response.success && response.data.html) {
                        const $newItems = $(response.data.html);
                        $newItems.hide();
                        $('#scholarships-grid').append($newItems);
                        $newItems.fadeIn(600);
                        
                        if (response.data.has_more) {
                            $button.data('page', page + 1)
                                   .removeClass('loading')
                                   .text('Tải thêm học bổng');
                        } else {
                            $button.fadeOut(300, function() {
                                $(this).remove();
                            });
                        }
                        
                        // Update results count
                        ScholarshipArchive.updateResultsCount();
                    } else {
                        $button.fadeOut(300, function() {
                            $(this).remove();
                        });
                    }
                },
                error: function() {
                    $button.removeClass('loading').text('Tải thêm học bổng');
                    console.error('Error loading more scholarships');
                }
            });
        },

        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };

    // Scholarship Single Page functionality
    const ScholarshipSingle = {
        init: function() {
            this.initApplicationProcess();
            this.initRequirements();
            this.initStickyInfo();
        },

        initApplicationProcess: function() {
            // Animate process steps on scroll
            $(window).on('scroll', this.animateProcessSteps.bind(this));
        },

        animateProcessSteps: function() {
            $('.process-step').each(function() {
                const $step = $(this);
                const elementTop = $step.offset().top;
                const elementBottom = elementTop + $step.outerHeight();
                const viewportTop = $(window).scrollTop();
                const viewportBottom = viewportTop + $(window).height();
                
                if (elementBottom > viewportTop && elementTop < viewportBottom) {
                    $step.addClass('animate-in');
                }
            });
        },

        initRequirements: function() {
            // Add hover effects to requirement items
            $('.requirement-item').on('mouseenter', function() {
                $(this).addClass('hover-effect');
            }).on('mouseleave', function() {
                $(this).removeClass('hover-effect');
            });
        },

        initStickyInfo: function() {
            // Make info card sticky on desktop
            if ($(window).width() > 1024) {
                const $infoCard = $('.quick-info-card');
                if ($infoCard.length) {
                    const originalTop = $infoCard.offset().top;
                    
                    $(window).on('scroll', function() {
                        const scrollTop = $(window).scrollTop();
                        const headerHeight = 100; // Adjust based on your header height
                        
                        if (scrollTop > originalTop - headerHeight) {
                            $infoCard.addClass('sticky-active');
                        } else {
                            $infoCard.removeClass('sticky-active');
                        }
                    });
                }
            }
        }
    };

    // Scholarship comparison functionality
    const ScholarshipComparison = {
        init: function() {
            this.selectedScholarships = [];
            this.bindEvents();
        },

        bindEvents: function() {
            $(document).on('click', '.compare-scholarship', this.addToComparison.bind(this));
            $(document).on('click', '.remove-from-comparison', this.removeFromComparison.bind(this));
            $(document).on('click', '.view-comparison', this.viewComparison.bind(this));
        },

        addToComparison: function(e) {
            e.preventDefault();
            const scholarshipId = $(e.currentTarget).data('scholarship-id');
            
            if (this.selectedScholarships.length < 3 && !this.selectedScholarships.includes(scholarshipId)) {
                this.selectedScholarships.push(scholarshipId);
                this.updateComparisonUI();
            }
        },

        removeFromComparison: function(e) {
            e.preventDefault();
            const scholarshipId = $(e.currentTarget).data('scholarship-id');
            const index = this.selectedScholarships.indexOf(scholarshipId);
            
            if (index > -1) {
                this.selectedScholarships.splice(index, 1);
                this.updateComparisonUI();
            }
        },

        updateComparisonUI: function() {
            // Update comparison counter
            $('.comparison-count').text(this.selectedScholarships.length);
            
            // Show/hide comparison bar
            if (this.selectedScholarships.length > 0) {
                $('.comparison-bar').fadeIn();
            } else {
                $('.comparison-bar').fadeOut();
            }
            
            // Update button states
            $('.compare-scholarship').each(function() {
                const scholarshipId = $(this).data('scholarship-id');
                if (this.selectedScholarships.includes(scholarshipId)) {
                    $(this).addClass('selected').text('Đã chọn');
                } else {
                    $(this).removeClass('selected').text('So sánh');
                }
            }.bind(this));
        },

        viewComparison: function(e) {
            e.preventDefault();
            // Implement comparison view logic
            console.log('Comparing scholarships:', this.selectedScholarships);
        }
    };

    // Initialize on document ready
    $(document).ready(function() {
        // Check if we're on scholarship archive page
        if ($('body').hasClass('post-type-archive-scholarship') || $('#scholarships-grid').length) {
            ScholarshipArchive.init();
        }
        
        // Check if we're on scholarship single page
        if ($('body').hasClass('single-scholarship') || $('.scholarship-hero').length) {
            ScholarshipSingle.init();
        }
        
        // Initialize comparison functionality
        ScholarshipComparison.init();
        
        // Smooth scrolling for anchor links
        $('a[href^="#"]').on('click', function(e) {
            const target = $(this.getAttribute('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800);
            }
        });
    });

    // Expose to global scope if needed
    window.ScholarshipArchive = ScholarshipArchive;
    window.ScholarshipSingle = ScholarshipSingle;
    window.ScholarshipComparison = ScholarshipComparison;

})(jQuery);
