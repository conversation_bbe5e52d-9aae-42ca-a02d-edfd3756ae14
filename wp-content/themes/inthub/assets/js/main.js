/**
 * Main JavaScript file for IntHub theme
 * 
 * @package IntHub
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        initializeTheme();
    });

    // Window load
    $(window).on('load', function() {
        handleWindowLoad();
    });

    // Window resize
    $(window).on('resize', function() {
        handleWindowResize();
    });

    /**
     * Initialize theme functionality
     */
    function initializeTheme() {
        initSmoothScrolling();
        initScrollToTop();
        initAnimations();
        initContactForm();
        initSearchForm();
        initEventFunctionality();
    }

    /**
     * Handle window load events
     */
    function handleWindowLoad() {
        // Hide loading spinner if exists
        $('.loading-spinner').fadeOut();
        
        // Initialize AOS animations if library is loaded
        if (typeof AOS !== 'undefined') {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                offset: 100
            });
        }
    }

    /**
     * Handle window resize events
     */
    function handleWindowResize() {
        // Recalculate hero slider height if needed
        adjustHeroSliderHeight();
    }

    /**
     * Initialize smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        $('a[href*="#"]:not([href="#"])').on('click', function(e) {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && 
                location.hostname === this.hostname) {
                
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                
                if (target.length) {
                    e.preventDefault();
                    
                    var headerHeight = $('.site-header').outerHeight() || 0;
                    var scrollTop = target.offset().top - headerHeight - 20;
                    
                    $('html, body').animate({
                        scrollTop: scrollTop
                    }, 800, 'swing');
                }
            }
        });
    }

    /**
     * Initialize scroll to top button
     */
    function initScrollToTop() {
        // Create scroll to top button if it doesn't exist
        if (!$('.scroll-to-top').length) {
            $('body').append('<button class="scroll-to-top fixed bottom-6 right-6 w-12 h-12 bg-pink-500 text-white rounded-full shadow-lg hover:bg-pink-600 transition duration-300 z-50 opacity-0 invisible" aria-label="Scroll to top"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path></svg></button>');
        }

        var $scrollToTop = $('.scroll-to-top');

        // Show/hide scroll to top button
        $(window).on('scroll', function() {
            if ($(this).scrollTop() > 300) {
                $scrollToTop.removeClass('opacity-0 invisible').addClass('opacity-100 visible');
            } else {
                $scrollToTop.removeClass('opacity-100 visible').addClass('opacity-0 invisible');
            }
        });

        // Scroll to top functionality
        $scrollToTop.on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: 0
            }, 600);
        });
    }

    /**
     * Initialize animations
     */
    function initAnimations() {
        // Fade in elements on scroll
        $(window).on('scroll', function() {
            $('.fade-in-up').each(function() {
                var elementTop = $(this).offset().top;
                var elementBottom = elementTop + $(this).outerHeight();
                var viewportTop = $(window).scrollTop();
                var viewportBottom = viewportTop + $(window).height();

                if (elementBottom > viewportTop && elementTop < viewportBottom) {
                    $(this).addClass('animate-fade-in-up');
                }
            });
        });

        // Counter animation
        $('.counter').each(function() {
            var $this = $(this);
            var countTo = $this.attr('data-count');

            $({ countNum: $this.text() }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'linear',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(this.countNum);
                }
            });
        });
    }

    /**
     * Initialize contact form
     */
    function initContactForm() {
        $('.contact-form').on('submit', function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $submitBtn = $form.find('button[type="submit"]');
            var originalText = $submitBtn.text();
            
            // Basic validation
            var isValid = true;
            $form.find('input[required], textarea[required]').each(function() {
                if (!$(this).val().trim()) {
                    isValid = false;
                    $(this).addClass('border-red-500');
                } else {
                    $(this).removeClass('border-red-500');
                }
            });
            
            // Email validation
            var email = $form.find('input[type="email"]').val();
            if (email && !isValidEmail(email)) {
                isValid = false;
                $form.find('input[type="email"]').addClass('border-red-500');
            }
            
            if (!isValid) {
                showNotification('Vui lòng điền đầy đủ thông tin hợp lệ.', 'error');
                return;
            }
            
            // Show loading state
            $submitBtn.text('Đang gửi...').prop('disabled', true);
            
            // Simulate form submission (replace with actual AJAX call)
            setTimeout(function() {
                $submitBtn.text(originalText).prop('disabled', false);
                $form[0].reset();
                showNotification('Cảm ơn bạn! Chúng tôi sẽ liên hệ với bạn sớm nhất.', 'success');
            }, 2000);
        });
    }

    /**
     * Initialize search form enhancements
     */
    function initSearchForm() {
        $('.search-form input[type="search"]').on('focus', function() {
            $(this).parent().addClass('ring-2 ring-pink-500');
        }).on('blur', function() {
            $(this).parent().removeClass('ring-2 ring-pink-500');
        });
    }

    /**
     * Adjust hero slider height
     */
    function adjustHeroSliderHeight() {
        var windowHeight = $(window).height();
        var headerHeight = $('.site-header').outerHeight() || 0;
        var minHeight = Math.max(windowHeight - headerHeight, 500);
        
        $('.hero-slider').css('min-height', minHeight + 'px');
    }

    /**
     * Validate email address
     */
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Show notification
     */
    function showNotification(message, type) {
        type = type || 'info';
        
        var bgColor = type === 'success' ? 'bg-green-500' : 
                     type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        
        var $notification = $('<div class="notification fixed top-4 right-4 ' + bgColor + ' text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300">' + message + '</div>');
        
        $('body').append($notification);
        
        // Animate in
        setTimeout(function() {
            $notification.removeClass('translate-x-full');
        }, 100);
        
        // Auto remove
        setTimeout(function() {
            $notification.addClass('translate-x-full');
            setTimeout(function() {
                $notification.remove();
            }, 300);
        }, 5000);
    }

    /**
     * Utility function to debounce events
     */
    function debounce(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    /**
     * Initialize event functionality
     */
    function initEventFunctionality() {
        initEventFilters();
        initEventAdminEnhancements();
        initEventCalendarIntegration();
    }

    /**
     * Initialize event filters on archive page
     */
    function initEventFilters() {
        $('.filter-btn').on('click', function() {
            var filter = $(this).data('filter');
            var $eventCards = $('.event-card');

            // Update active button
            $('.filter-btn').removeClass('active');
            $(this).addClass('active');

            // Filter events with animation
            $eventCards.fadeOut(200, function() {
                if (filter === 'all') {
                    $eventCards.fadeIn(200);
                } else {
                    $eventCards.filter('.' + filter).fadeIn(200);
                }
            });
        });
    }

    /**
     * Initialize event admin enhancements
     */
    function initEventAdminEnhancements() {
        // Character counter for short description
        $('#event_short_description').on('input', function() {
            var length = $(this).val().length;
            var $counter = $('#char-count');

            if ($counter.length) {
                $counter.text(length + '/200');

                if (length > 200) {
                    $counter.css('color', 'red');
                    $(this).css('border-color', 'red');
                } else {
                    $counter.css('color', 'inherit');
                    $(this).css('border-color', '');
                }
            }
        });

        // Validate datetime fields
        $('#event_start_datetime, #event_end_datetime').on('change', function() {
            var startDate = new Date($('#event_start_datetime').val());
            var endDate = new Date($('#event_end_datetime').val());

            if (startDate && endDate && startDate >= endDate) {
                showNotification('Thời gian kết thúc phải sau thời gian bắt đầu!', 'error');
                $(this).focus();
            }
        });

        // Auto-fill end date when start date is selected
        $('#event_start_datetime').on('change', function() {
            var startDate = new Date($(this).val());
            var $endDate = $('#event_end_datetime');

            if (startDate && !$endDate.val()) {
                // Default to 2 hours later
                var endDate = new Date(startDate.getTime() + (2 * 60 * 60 * 1000));
                var endDateString = endDate.toISOString().slice(0, 16);
                $endDate.val(endDateString);
            }
        });
    }

    /**
     * Initialize event calendar integration
     */
    function initEventCalendarIntegration() {
        // Add to calendar functionality
        $('.add-to-calendar-btn').on('click', function(e) {
            e.preventDefault();

            var eventData = $(this).data();
            if (eventData.startDate && eventData.endDate) {
                var googleCalendarUrl = generateGoogleCalendarUrl(eventData);
                window.open(googleCalendarUrl, '_blank');
            }
        });
    }

    /**
     * Generate Google Calendar URL
     */
    function generateGoogleCalendarUrl(eventData) {
        var formatDate = function(date) {
            return new Date(date).toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
        };

        var params = {
            action: 'TEMPLATE',
            text: encodeURIComponent(eventData.title || ''),
            dates: formatDate(eventData.startDate) + '/' + formatDate(eventData.endDate),
            location: encodeURIComponent(eventData.location || ''),
            details: encodeURIComponent(eventData.description || '')
        };

        var queryString = Object.keys(params).map(key => key + '=' + params[key]).join('&');
        return 'https://calendar.google.com/calendar/render?' + queryString;
    }

    /**
     * Utility function to throttle events
     */
    function throttle(func, limit) {
        var inThrottle;
        return function() {
            var args = arguments;
            var context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() {
                    inThrottle = false;
                }, limit);
            }
        };
    }

    // Expose functions globally if needed
    window.IntHub = {
        showNotification: showNotification,
        debounce: debounce,
        throttle: throttle,
        generateGoogleCalendarUrl: generateGoogleCalendarUrl
    };

})(jQuery);
