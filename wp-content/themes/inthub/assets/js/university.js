/**
 * University JavaScript
 *
 * @package IntHub
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // University Archive functionality
    const UniversityArchive = {
        init: function() {
            this.bindEvents();
            this.updateResultsCount();
        },

        bindEvents: function() {
            // Filter events
            $('#country-filter').on('change', this.filterUniversities.bind(this));
            $('#search-filter').on('input', this.debounce(this.filterUniversities.bind(this), 300));
            
            // Reset filters
            $(document).on('click', '.reset-filters', this.resetFilters.bind(this));
            
            // Load more functionality (if needed)
            $(document).on('click', '.load-more-universities', this.loadMore.bind(this));
        },

        filterUniversities: function() {
            const countryFilter = $('#country-filter').val().toLowerCase();
            const searchFilter = $('#search-filter').val().toLowerCase();
            
            let visibleCount = 0;
            
            $('.university-card').each(function() {
                const $card = $(this);
                const country = $card.data('country') || '';
                const name = $card.data('name') || '';
                
                let showCard = true;
                
                // Country filter
                if (countryFilter && country !== countryFilter) {
                    showCard = false;
                }
                
                // Search filter
                if (searchFilter && name.indexOf(searchFilter) === -1) {
                    showCard = false;
                }
                
                if (showCard) {
                    $card.removeClass('hidden').fadeIn(300);
                    visibleCount++;
                } else {
                    $card.addClass('hidden').fadeOut(300);
                }
            });
            
            this.updateResultsCount(visibleCount);
            this.showNoResults(visibleCount === 0);
        },

        updateResultsCount: function(count) {
            if (typeof count === 'undefined') {
                count = $('.university-card:not(.hidden)').length;
            }
            $('#results-count').text(count);
        },

        showNoResults: function(show) {
            if (show) {
                if ($('.no-results').length === 0) {
                    const noResultsHtml = `
                        <div class="no-results col-span-full text-center py-12">
                            <div class="text-gray-500">
                                <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <h3 class="text-xl font-medium mb-2">Không tìm thấy trường đại học nào</h3>
                                <p>Vui lòng thử lại với từ khóa khác hoặc điều chỉnh bộ lọc.</p>
                                <button class="reset-filters mt-4 inline-flex items-center text-blue-600 hover:text-blue-800">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Đặt lại bộ lọc
                                </button>
                            </div>
                        </div>
                    `;
                    $('#universities-grid').append(noResultsHtml);
                }
            } else {
                $('.no-results').remove();
            }
        },

        resetFilters: function() {
            $('#country-filter').val('');
            $('#search-filter').val('');
            $('.university-card').removeClass('hidden').fadeIn(300);
            this.updateResultsCount();
            this.showNoResults(false);
        },

        loadMore: function(e) {
            e.preventDefault();
            const $button = $(e.currentTarget);
            const page = $button.data('page') || 2;
            
            $button.addClass('loading').text('Đang tải...');
            
            $.ajax({
                url: inthub_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'load_more_universities',
                    page: page,
                    nonce: inthub_ajax.nonce
                },
                success: function(response) {
                    if (response.success && response.data.html) {
                        $('#universities-grid').append(response.data.html);
                        
                        if (response.data.has_more) {
                            $button.data('page', page + 1).removeClass('loading').text('Tải thêm');
                        } else {
                            $button.remove();
                        }
                    } else {
                        $button.remove();
                    }
                },
                error: function() {
                    $button.removeClass('loading').text('Tải thêm');
                }
            });
        },

        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };

    // University Single Page functionality
    const UniversitySingle = {
        init: function() {
            this.initMap();
            this.initVideo();
            this.initScrollToScholarships();
        },

        initMap: function() {
            // Make map responsive
            $('.map-container iframe').each(function() {
                const $iframe = $(this);
                if (!$iframe.attr('width') || !$iframe.attr('height')) {
                    $iframe.attr('width', '100%').attr('height', '300');
                }
            });
        },

        initVideo: function() {
            // Make videos responsive
            $('.video-container iframe').each(function() {
                const $iframe = $(this);
                const src = $iframe.attr('src');
                
                // Add autoplay=0 and rel=0 for YouTube videos
                if (src && src.includes('youtube.com')) {
                    const separator = src.includes('?') ? '&' : '?';
                    const newSrc = src + separator + 'autoplay=0&rel=0&modestbranding=1';
                    $iframe.attr('src', newSrc);
                }
            });
        },

        initScrollToScholarships: function() {
            $('a[href="#scholarships"]').on('click', function(e) {
                e.preventDefault();
                const target = $('#scholarships');
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 800);
                }
            });
        }
    };

    // AJAX Load More Universities
    function loadMoreUniversities() {
        $(document).on('click', '.load-more-universities', function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const page = $button.data('page') || 2;
            const $grid = $('#universities-grid');
            
            $button.addClass('loading').text('Đang tải...');
            
            $.ajax({
                url: inthub_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'load_more_universities',
                    page: page,
                    nonce: inthub_ajax.nonce,
                    country: $('#country-filter').val(),
                    search: $('#search-filter').val()
                },
                success: function(response) {
                    if (response.success && response.data.html) {
                        const $newItems = $(response.data.html);
                        $newItems.hide();
                        $grid.append($newItems);
                        $newItems.fadeIn(600);
                        
                        if (response.data.has_more) {
                            $button.data('page', page + 1)
                                   .removeClass('loading')
                                   .text('Tải thêm trường đại học');
                        } else {
                            $button.fadeOut(300, function() {
                                $(this).remove();
                            });
                        }
                        
                        // Update results count
                        UniversityArchive.updateResultsCount();
                    } else {
                        $button.fadeOut(300, function() {
                            $(this).remove();
                        });
                    }
                },
                error: function() {
                    $button.removeClass('loading').text('Tải thêm trường đại học');
                    console.error('Error loading more universities');
                }
            });
        });
    }

    // Smooth scrolling for anchor links
    function initSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            const target = $(this.getAttribute('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800);
            }
        });
    }

    // Initialize on document ready
    $(document).ready(function() {
        // Check if we're on university archive page
        if ($('body').hasClass('post-type-archive-university') || $('#universities-grid').length) {
            UniversityArchive.init();
            loadMoreUniversities();
        }
        
        // Check if we're on university single page
        if ($('body').hasClass('single-university') || $('.university-hero').length) {
            UniversitySingle.init();
        }
        
        // Initialize smooth scrolling
        initSmoothScrolling();
    });

    // Expose to global scope if needed
    window.UniversityArchive = UniversityArchive;
    window.UniversitySingle = UniversitySingle;

})(jQuery);
