/**
 * Scholarship Styles
 *
 * @package IntHub
 * @since 1.0.0
 */

/* Scholarship Card Styles */
.scholarship-card {
    position: relative;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.scholarship-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: #fa3c80;
}

.scholarship-card.hidden {
    display: none;
}

/* Scholarship Icon */
.scholarship-icon {
    width: 3rem;
    height: 3rem;
    background: rgba(250, 60, 128, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.scholarship-icon svg {
    width: 1.5rem;
    height: 1.5rem;
    color: #fa3c80;
}

/* Scholarship Value */
.scholarship-value {
    display: flex;
    align-items: center;
    color: #fa3c80;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.75rem;
}

.scholarship-value svg {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

/* University Link */
.university-link {
    display: flex;
    align-items: center;
    color: #6b7280;
    font-size: 0.875rem;
    text-decoration: none;
    margin-bottom: 0.75rem;
    transition: color 0.2s ease;
}

.university-link:hover {
    color: #004aad;
}

.university-link svg {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
}

/* Support Info */
.support-info {
    background: #f9fafb;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.support-info-label {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.support-info-value {
    font-size: 0.875rem;
    color: #374151;
}

/* Scholarship Grid */
#scholarships-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
}

@media (max-width: 768px) {
    #scholarships-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* Scholarship Single Page Styles */
.scholarship-hero {
    position: relative;
    height: 24rem;
    overflow: hidden;
}

.scholarship-hero-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, rgba(250, 60, 128, 0.8), rgba(0, 74, 173, 0.8));
}

.scholarship-hero-content {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    color: white;
}

/* Scholarship Badge */
.scholarship-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-right: 1rem;
}

/* Country Badge */
.country-badge {
    background: #004aad;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    position: absolute;
    top: 1rem;
    right: 1rem;
}

/* Requirements Grid */
.requirements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.requirement-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
}

.requirement-item svg {
    width: 1.25rem;
    height: 1.25rem;
    color: #fa3c80;
    margin-right: 0.75rem;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.requirement-title {
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.25rem;
}

.requirement-description {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Application Process */
.process-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.process-number {
    width: 2rem;
    height: 2rem;
    background: #004aad;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.process-number.final {
    background: #fa3c80;
}

.process-content h3 {
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.5rem;
}

.process-content p {
    color: #6b7280;
    line-height: 1.5;
}

/* Filter Styles */
.scholarship-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    justify-content: space-between;
    padding: 2rem 0;
    background: white;
    border-bottom: 1px solid #e5e7eb;
}

.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.filter-group select,
.filter-group input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
    min-width: 12rem;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #004aad;
    box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
}

/* Results Count */
.results-count {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Line Clamp Utility */
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Date Display */
.date-display {
    display: flex;
    align-items: center;
    color: #6b7280;
    font-size: 0.75rem;
}

.date-display svg {
    width: 1rem;
    height: 1rem;
    margin-right: 0.25rem;
}

/* Action Buttons */
.action-button {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.action-button-primary {
    background: #004aad;
    color: white;
}

.action-button-primary:hover {
    background: rgba(0, 74, 173, 0.9);
}

.action-button-secondary {
    background: #fa3c80;
    color: white;
}

.action-button-secondary:hover {
    background: rgba(250, 60, 128, 0.9);
}

.action-button svg {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .scholarship-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .filter-controls {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .scholarship-hero {
        height: 20rem;
    }
    
    .scholarship-hero-content h1 {
        font-size: 2rem;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-group select,
    .filter-group input {
        width: 100%;
        min-width: auto;
    }
    
    .requirements-grid {
        grid-template-columns: 1fr;
    }
    
    .action-button {
        width: 100%;
        justify-content: center;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #fa3c80;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.scholarship-card:focus-within {
    outline: 2px solid #fa3c80;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .scholarship-filters,
    .pagination {
        display: none;
    }
    
    .scholarship-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}
