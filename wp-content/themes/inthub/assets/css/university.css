/**
 * University Styles
 *
 * @package IntHub
 * @since 1.0.0
 */

/* University Card Styles */
.university-card {
    position: relative;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.university-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.university-card.hidden {
    display: none;
}

/* Filter Styles */
.filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #004aad;
    box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
}

/* University Grid */
#universities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
}

@media (max-width: 768px) {
    #universities-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* University Info Styles */
.university-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.university-info-item svg {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

/* Country Badge */
.country-badge {
    background: #004aad;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    position: absolute;
    top: 1rem;
    right: 1rem;
}

/* Scholarship Count */
.scholarship-count {
    display: flex;
    align-items: center;
    color: #fa3c80;
    font-size: 0.875rem;
    font-weight: 500;
}

.scholarship-count svg {
    width: 1rem;
    height: 1rem;
    margin-right: 0.25rem;
}

/* University Single Page Styles */
.university-hero {
    position: relative;
    height: 24rem;
    overflow: hidden;
}

.university-hero-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, rgba(0, 74, 173, 0.8), rgba(250, 60, 128, 0.8));
}

.university-hero-content {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    color: white;
}

/* Quick Info Sidebar */
.quick-info-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    position: sticky;
    top: 2rem;
}

.quick-info-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.quick-info-item svg {
    width: 1.25rem;
    height: 1.25rem;
    color: #fa3c80;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.quick-info-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.quick-info-value {
    font-weight: 500;
    color: #111827;
}

/* Course List */
.course-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.course-item svg {
    width: 1.25rem;
    height: 1.25rem;
    color: #fa3c80;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

/* Video Container */
.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
    border-radius: 8px;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Map Container */
.map-container {
    border-radius: 8px;
    overflow: hidden;
}

.map-container iframe {
    width: 100%;
    height: 300px;
    border: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .quick-info-card {
        position: static;
        margin-top: 2rem;
    }
}

@media (max-width: 768px) {
    .university-hero {
        height: 20rem;
    }
    
    .university-hero-content h1 {
        font-size: 2rem;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-group select,
    .filter-group input {
        width: 100%;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #004aad;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* No Results */
.no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.no-results svg {
    width: 4rem;
    height: 4rem;
    margin: 0 auto 1rem;
    opacity: 0.5;
}

.no-results h3 {
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #374151;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 3rem;
}

.pagination a,
.pagination span {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    text-decoration: none;
    color: #374151;
    transition: all 0.2s ease;
}

.pagination a:hover {
    background: #004aad;
    color: white;
    border-color: #004aad;
}

.pagination .current {
    background: #004aad;
    color: white;
    border-color: #004aad;
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.university-card:focus-within {
    outline: 2px solid #004aad;
    outline-offset: 2px;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: 2px solid #004aad;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .filter-group,
    .pagination {
        display: none;
    }
    
    .university-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}
