<?php
$args = array(
    'post_type' => 'event',
    'posts_per_page' => 5,
    'post_status' => 'publish',
    'order' => "ASC",
    'orderby' => 'meta_value',
    'meta_key' => 'event_start_datetime',
    'meta_query' => array(
        array(
            'key' => 'event_start_datetime',
            'value' => date('Y-m-d H:i:s'),
            'compare' => '>=',
            'type' => 'DATETIME'
        )
    )
);

$latest_posts = new WP_Query($args);
?>
<div>
    <h3 class="text-2xl font-semibold text-[#004aad] mb-6">Sự kiện sắp diễn ra</h3>

    <div class="space-y-4">
        <?php if ($latest_posts->have_posts()) : ?>
            <?php while ($latest_posts->have_posts()) : $latest_posts->the_post(); ?>
            <!-- Event 1 -->
            <div class="event-card bg-white rounded-lg shadow-md p-5 hover:bg-gray-50">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-semibold text-[#004aad]">
                        <a href="<?php the_permalink(); ?>" ><?php the_title(); ?></a>
                    </h4>
                    <span class="text-[#fa3c80] text-sm font-medium"><?php echo esc_html(get_post_meta(get_the_ID(), 'event_start_datetime', true));?></span>
                </div>
                <p class="text-[#004aad] opacity-70 text-sm mb-3"><?php echo esc_html(get_post_meta(get_the_ID(), 'event_short_description', true));?></p>
                <div class="flex items-center text-[#004aad] opacity-70 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span><?php echo esc_html(get_post_meta(get_the_ID(), 'event_location', true));?></span>
                </div>
            </div>
            <?php endwhile; ?>
            <?php wp_reset_postdata(); ?>
        <?php endif; ?>
    </div>

    <div class="mt-8">
        <a href="<?php echo esc_url(get_post_type_archive_link('event')); ?>" class="inline-flex items-center justify-center w-full border-2 border-[#fa3c80] text-[#fa3c80] font-semibold py-3 px-6 rounded-lg hover:bg-[#fa3c80] hover:text-white transition duration-300">
            Xem tất cả sự kiện
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
        </a>
    </div>
</div>
