<?php
// T<PERSON><PERSON> một truy vấn để lấy 4 bài viết danh mục tin tức mới nhất
$args = array(
    'post_type' => 'post',
    'posts_per_page' => 4,
    'post_status' => 'publish',
    'order' => 'DESC',
    'orderby' => 'date',
    'category_name' => 'tin-tuc'
);

$latest_posts = new WP_Query($args);
?>
<div class="lg:col-span-2">
    <h3 class="text-2xl font-semibold text-[#004aad] mb-6">Tin tức mới nhất</h3>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <?php if ($latest_posts->have_posts()) : ?>
            <?php while ($latest_posts->have_posts()) : $latest_posts->the_post(); ?>
                <div class="news-card bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="relative">
                        <div class="h-48 overflow-hidden">
                            <?php if (has_post_thumbnail()) : ?>

                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('full', array('class' => 'news-image w-full h-full')); ?>
                                </a>

                            <?php else : ?>
                                <svg class="news-image w-full h-full" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="400" height="200" fill="#fa3c80" opacity="0.1"></rect>
                                    <circle cx="200" cy="100" r="80" fill="#fa3c80" opacity="0.2"></circle>
                                    <path d="M50,50 Q200,0 350,50 Q400,150 350,150 Q200,200 50,150 Q0,50 50,50" fill="#fa3c80" opacity="0.1"></path>
                                    <g transform="translate(150, 70)">
                                        <rect x="0" y="0" width="100" height="70" rx="5" fill="#ffffff" stroke="#004aad" stroke-width="2"></rect>
                                        <path d="M20,20 L80,20 M20,35 L80,35 M20,50 L60,50" stroke="#004aad" stroke-width="2" stroke-linecap="round"></path>
                                    </g>
                                    <g transform="translate(120, 90)">
                                        <circle cx="15" cy="15" r="15" fill="#ffffff" stroke="#fa3c80" stroke-width="2"></circle>
                                        <path d="M10,15 L15,20 L22,12" stroke="#fa3c80" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </g>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <div class="news-date"><?php echo get_the_date(); ?></div>
                    </div>
                    <div class="p-5">
                        <h4 class="text-lg font-semibold text-[#004aad] mb-2">
                            <a href="<?php the_permalink(); ?>" ><?php the_title(); ?></a>
                        </h4>
                        <p class="text-[#004aad] opacity-70 text-sm mb-4"><?php the_excerpt(); ?></p>
                        <a href="<?php the_permalink(); ?>" class="inline-flex items-center text-[#fa3c80] font-medium text-sm">
                            Đọc thêm
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            <?php endwhile; ?>
            <?php wp_reset_postdata(); ?>
        <?php endif; ?>
    </div>

    <div class="mt-8 text-center">
        <a href="<?php echo esc_url(get_category_link(get_category_by_slug('tin-tuc')->term_id)); ?>" class="inline-flex items-center justify-center bg-[#fa3c80] text-white font-semibold py-3 px-6 rounded-lg hover:bg-[#e02e6c] transition duration-300">
            Xem tất cả tin tức
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
        </a>
    </div>
</div>
