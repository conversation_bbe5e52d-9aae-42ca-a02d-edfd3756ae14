# Hướng Dẫn Sử Dụng Custom Post Types University & Scholarship

## Tổng Quan

Hệ thống này đã được triển khai với 2 custom post types chính:
- **University (Trường Đại Học)**: <PERSON>u<PERSON>n lý thông tin các trường đại học
- **Scholarship (Học Bổng)**: <PERSON><PERSON><PERSON><PERSON> lý thông tin học bổng với mối quan hệ với trường đại học

## 1. Cấu Trúc Dữ Liệu

### University (Trường Đại Học)
**Custom Fields:**
- `university_name` - Tên trường đại học (Text, Required)
- `establishment_year` - <PERSON><PERSON><PERSON> thành lập (Number)
- `location` - Đ<PERSON><PERSON> điể<PERSON> (Text, Required)
- `overview` - Tổ<PERSON> quan (WYSIWYG Editor)
- `website_url` - Website (URL)
- `courses` - <PERSON><PERSON><PERSON><PERSON> <PERSON> (Textarea)
- `country` - Q<PERSON><PERSON><PERSON> gia (Select dropdown, Required)
- `tuition_fee` - <PERSON><PERSON><PERSON>h<PERSON> (Text)
- `admission_info` - Th<PERSON><PERSON> tin tuyển sinh (WYSIWYG Editor)
- `map` - <PERSON><PERSON><PERSON> đồ Google Maps (Textarea)
- `video_url` - Link video giới thiệu (URL)

### Scholarship (Học Bổng)
**Custom Fields:**
- `scholarship_name` - Tên học bổng (Text, Required)
- `tuition_support` - Học phí được hỗ trợ (Text)
- `scholarship_value` - Giá trị học bổng (Text, Required)
- `related_university` - Trường đại học liên quan (Post relationship, Required)

## 2. Cách Sử Dụng Admin

### Thêm Trường Đại Học Mới
1. Vào **Admin Dashboard** → **Trường Đại Học** → **Thêm trường mới**
2. Điền thông tin cơ bản:
   - Tiêu đề bài viết (sẽ là tên hiển thị)
   - Nội dung (mô tả ngắn)
   - Ảnh đại diện (Featured Image)
3. Điền **Chi tiết trường đại học**:
   - Tên trường đại học (bắt buộc)
   - Năm thành lập
   - Địa điểm (bắt buộc)
   - Quốc gia (bắt buộc)
   - Website
   - Học phí
   - Link video giới thiệu
4. Điền **Thông tin chi tiết**:
   - Tổng quan (sử dụng WYSIWYG editor)
   - Khóa học (mỗi khóa học một dòng)
   - Thông tin tuyển sinh
   - Bản đồ Google Maps (paste embed code)

### Thêm Học Bổng Mới
1. Vào **Admin Dashboard** → **Học Bổng** → **Thêm học bổng mới**
2. Điền thông tin cơ bản:
   - Tiêu đề bài viết
   - Nội dung (mô tả chi tiết học bổng)
   - Ảnh đại diện
3. Điền **Chi tiết học bổng**:
   - Tên học bổng (bắt buộc)
   - Trường đại học liên quan (bắt buộc)
   - Giá trị học bổng (bắt buộc)
   - Học phí được hỗ trợ

## 3. Tổ Chức Tài Nguyên

### CSS Files
- `wp-content/themes/inthub/assets/css/university.css` - Styles cho University
- `wp-content/themes/inthub/assets/css/scholarship.css` - Styles cho Scholarship

### JavaScript Files
- `wp-content/themes/inthub/assets/js/university.js` - JavaScript cho University
- `wp-content/themes/inthub/assets/js/scholarship.js` - JavaScript cho Scholarship

### Template Files
- `archive-university.php` - Danh sách trường đại học
- `single-university.php` - Chi tiết trường đại học
- `archive-scholarship.php` - Danh sách học bổng
- `single-scholarship.php` - Chi tiết học bổng

## 4. Tính Năng Chính

### Trang Danh Sách Trường Đại Học
- **URL**: `/truong-dai-hoc/`
- **Tính năng**:
  - Lọc theo quốc gia
  - Tìm kiếm theo tên trường
  - Hiển thị số học bổng có sẵn
  - Responsive design
  - Pagination

### Trang Chi Tiết Trường Đại Học
- **URL**: `/truong-dai-hoc/[slug]/`
- **Tính năng**:
  - Hiển thị đầy đủ thông tin trường
  - Danh sách học bổng liên quan
  - Video giới thiệu
  - Bản đồ Google Maps
  - Thông tin nhanh (sidebar)

### Trang Danh Sách Học Bổng
- **URL**: `/hoc-bong/`
- **Tính năng**:
  - Lọc theo trường đại học
  - Lọc theo quốc gia
  - Lọc theo giá trị học bổng (toàn phần/một phần)
  - Tìm kiếm theo tên học bổng
  - Responsive design

### Trang Chi Tiết Học Bổng
- **URL**: `/hoc-bong/[slug]/`
- **Tính năng**:
  - Thông tin chi tiết học bổng
  - Yêu cầu ứng tuyển
  - Quy trình ứng tuyển
  - Link đến trường đại học liên quan

## 5. Mối Quan Hệ Dữ Liệu

### One-to-Many Relationship
- Một trường đại học có thể có nhiều học bổng
- Mỗi học bổng chỉ thuộc về một trường đại học
- Khi xóa trường đại học, cần xem xét các học bổng liên quan

### Hiển Thị Liên Kết
- Trang trường đại học hiển thị tất cả học bổng liên quan
- Trang học bổng có link đến trường đại học
- Admin columns hiển thị số lượng học bổng cho mỗi trường

## 6. Tính Năng Tìm Kiếm và Lọc

### JavaScript Filtering
- Real-time filtering không cần reload trang
- Debounced search (300ms delay)
- Update results count
- Show/hide "no results" message

### AJAX Load More
- Load thêm nội dung không cần reload
- Maintain filter state
- Loading indicators

## 7. Responsive Design

### Breakpoints
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

### Mobile Optimizations
- Stacked layouts
- Touch-friendly buttons
- Simplified filters
- Optimized images

## 8. SEO và Performance

### URL Structure
- Clean, SEO-friendly URLs
- Vietnamese slugs supported
- Proper canonical URLs

### Meta Data
- Custom meta boxes for SEO
- Structured data (Schema.org)
- Open Graph tags

### Performance
- Lazy loading images
- Minified CSS/JS
- Optimized database queries

## 9. Customization

### Thêm Custom Fields Mới
1. Sửa file `functions.php`
2. Thêm field vào meta box callback
3. Thêm sanitization trong save function
4. Update template files để hiển thị

### Thêm Quốc Gia Mới
1. Sửa dropdown trong `inthub_university_details_callback()`
2. Update `$country_names` array trong templates
3. Update filter options trong archive templates

### Styling Customization
- Sửa file CSS tương ứng
- Sử dụng Tailwind CSS classes
- Maintain responsive design

## 10. Troubleshooting

### Common Issues
1. **Custom fields không hiển thị**: Kiểm tra nonce và permissions
2. **AJAX không hoạt động**: Kiểm tra `inthub_ajax` localization
3. **Styles không load**: Kiểm tra enqueue conditions
4. **Responsive issues**: Test trên nhiều devices

### Debug Tips
- Enable WordPress debug mode
- Check browser console for JS errors
- Verify AJAX responses
- Test with default theme

## 11. Maintenance

### Regular Tasks
- Backup database trước khi update
- Test functionality sau WordPress updates
- Optimize database periodically
- Monitor performance

### Updates
- Keep custom code documented
- Test in staging environment
- Maintain backward compatibility
- Update documentation

## 12. Tích Hợp Với Plugins

### Recommended Plugins
- **Yoast SEO**: Tối ưu SEO
- **WP Rocket**: Caching và performance
- **Smush**: Tối ưu hình ảnh
- **Contact Form 7**: Forms liên hệ

### Plugin Compatibility
- Tested với WordPress 6.0+
- Compatible với major SEO plugins
- Works với caching plugins
- Supports translation plugins

---

**Lưu ý**: Hệ thống này được thiết kế để dễ dàng mở rộng và tùy chỉnh. Khi thực hiện thay đổi, luôn backup và test trong môi trường staging trước.
