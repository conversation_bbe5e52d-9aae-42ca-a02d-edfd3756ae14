# Hướng dẫn sử dụng Custom Post Type "Sự kiện" - Theme InThub

## Tổng quan

Custom Post Type "Sự kiện" đã được tích hợp hoàn toàn vào WordPress theme InThub với đầy đủ chức năng quản lý và hiển thị sự kiện chuyên nghiệp.

## 1. Cấu hình Custom Post Type

### Thông tin cơ bản:
- **Tên post type**: `event`
- **Label hiển thị**: "Sự kiện" (tiếng Việt)
- **Slug URL**: `/su-kien/`
- **Menu icon**: Calendar (dashicons-calendar-alt)
- **Vị trí menu**: 5 (sau Posts)

### Tính năng hỗ trợ:
- ✅ Public queries
- ✅ Admin menu
- ✅ REST API
- ✅ Archive page
- ✅ Single post view
- ✅ Featured image
- ✅ Title và editor
- ✅ Custom fields
- ✅ Excerpt

## 2. Custom Fields (Meta Fields)

### Các trường bắt buộc:

#### 2.1 Thời gian bắt đầu (`event_start_datetime`)
- **Loại**: DateTime picker
- **B<PERSON>t buộc**: Có
- **Validation**: Tự động kiểm tra logic thời gian
- **Tính năng**: Auto-fill thời gian kết thúc (+2 giờ)

#### 2.2 Thời gian kết thúc (`event_end_datetime`)
- **Loại**: DateTime picker  
- **Bắt buộc**: Có
- **Validation**: Phải sau thời gian bắt đầu

#### 2.3 Địa điểm (`event_location`)
- **Loại**: Text field
- **Bắt buộc**: Có
- **Tính năng**: Tích hợp Google Maps link

#### 2.4 Mô tả ngắn (`event_short_description`)
- **Loại**: Textarea
- **Bắt buộc**: Có
- **Giới hạn**: 200 ký tự
- **Tính năng**: Real-time character counter

## 3. Giao diện Admin

### 3.1 Meta Box "Chi tiết sự kiện"
- Giao diện thân thiện với form validation
- Real-time character counter
- Datetime picker với validation logic
- Thông báo lỗi tức thời

### 3.2 Custom Columns trong Admin List
- **Thời gian bắt đầu**: Hiển thị ngày và giờ
- **Thời gian kết thúc**: Hiển thị ngày và giờ  
- **Địa điểm**: Hiển thị với tooltip (rút gọn nếu quá dài)
- **Trạng thái**: Tự động tính toán (Sắp diễn ra/Đang diễn ra/Đã kết thúc)

### 3.3 Sắp xếp và Lọc
- **Mặc định**: Sắp xếp theo thời gian bắt đầu (ASC)
- **Có thể sắp xếp**: Tất cả custom columns
- **Tìm kiếm**: Hỗ trợ tìm kiếm trong title và content

## 4. Frontend Display

### 4.1 Single Event Page (`single-event.php`)

#### Cấu trúc layout:
```
- Event Header (title, status badge, short description)
- Event Details Card (thời gian, địa điểm với icons)
- Featured Image
- Event Content (từ editor)
- Action Buttons (Google Maps, Add to Calendar)
- Social Share
- Event Navigation (prev/next events)
```

#### Tính năng đặc biệt:
- **Status Badge**: Tự động hiển thị trạng thái sự kiện
- **Google Maps Integration**: Link trực tiếp đến bản đồ
- **Calendar Integration**: Thêm vào Google Calendar
- **Structured Data**: Schema.org markup cho SEO
- **Responsive Design**: Tương thích mobile

### 4.2 Archive Events Page (`archive-event.php`)

#### Cấu trúc layout:
```
- Archive Header
- Event Filter Tabs (Tất cả/Sắp diễn ra/Đang diễn ra/Đã kết thúc)
- Events Grid (responsive 1-2-3 columns)
- Pagination
```

#### Tính năng filter:
- **JavaScript filtering**: Không reload trang
- **Smooth animations**: Fade in/out effects
- **Status-based filtering**: Tự động phân loại theo thời gian

## 5. Styling và CSS

### 5.1 CSS Classes chính:
```css
.event-card              /* Card container */
.filter-btn              /* Filter buttons */
.event-status-badge      /* Status badges */
.event-details-card      /* Single event details */
.event-action-buttons    /* Action buttons container */
```

### 5.2 Responsive Design:
- **Mobile**: 1 column grid
- **Tablet**: 2 columns grid  
- **Desktop**: 3 columns grid

### 5.3 Color Scheme:
- **Primary**: Blue (#1e3a8a)
- **Secondary**: Pink (#ec4899)
- **Success**: Green (#166534)
- **Warning**: Yellow (#d97706)
- **Danger**: Red (#dc2626)

## 6. JavaScript Functionality

### 6.1 Admin Enhancements:
- Character counter cho short description
- Datetime validation
- Auto-fill end time
- Real-time error messages

### 6.2 Frontend Features:
- Event filtering với animations
- Google Calendar integration
- Smooth scrolling
- Mobile-friendly interactions

## 7. SEO và Structured Data

### 7.1 Schema.org Markup:
```json
{
  "@context": "https://schema.org",
  "@type": "Event",
  "name": "Event Title",
  "description": "Event Description", 
  "startDate": "2024-01-01T10:00:00+07:00",
  "endDate": "2024-01-01T12:00:00+07:00",
  "location": {
    "@type": "Place",
    "name": "Event Location",
    "address": "Event Address"
  },
  "organizer": {
    "@type": "Organization", 
    "name": "Site Name",
    "url": "Site URL"
  }
}
```

### 7.2 SEO Benefits:
- Rich snippets trong search results
- Google Events integration
- Better search visibility
- Social media optimization

## 8. Cách sử dụng

### 8.1 Tạo sự kiện mới:
1. Vào **Admin → Sự kiện → Thêm sự kiện mới**
2. Nhập tiêu đề sự kiện
3. Thêm nội dung chi tiết trong editor
4. Upload featured image
5. Điền thông tin trong "Chi tiết sự kiện":
   - Thời gian bắt đầu (bắt buộc)
   - Thời gian kết thúc (bắt buộc) 
   - Địa điểm (bắt buộc)
   - Mô tả ngắn (bắt buộc, max 200 ký tự)
6. Publish

### 8.2 Quản lý sự kiện:
- **Xem tất cả**: Admin → Sự kiện
- **Sắp xếp**: Click vào column headers
- **Tìm kiếm**: Sử dụng search box
- **Bulk actions**: Select multiple events

### 8.3 Hiển thị frontend:
- **Archive page**: `/su-kien/`
- **Single event**: `/su-kien/ten-su-kien/`
- **Filter**: Sử dụng tabs trên archive page

## 9. Customization

### 9.1 Thêm custom fields:
```php
// Trong functions.php
function custom_event_fields($post) {
    // Thêm field mới
    $custom_field = get_post_meta($post->ID, 'custom_field', true);
    echo '<input type="text" name="custom_field" value="' . $custom_field . '">';
}
```

### 9.2 Modify templates:
- Copy `single-event.php` hoặc `archive-event.php`
- Customize theo nhu cầu
- Giữ nguyên structure cơ bản

### 9.3 Add custom CSS:
```css
/* Trong assets/css/custom.css */
.custom-event-style {
    /* Custom styles */
}
```

## 10. Troubleshooting

### 10.1 Lỗi thường gặp:
- **404 trên archive page**: Flush rewrite rules (Settings → Permalinks → Save)
- **Meta fields không lưu**: Check user permissions
- **JavaScript không hoạt động**: Check jQuery dependency

### 10.2 Debug:
- Enable WP_DEBUG trong wp-config.php
- Check browser console cho JavaScript errors
- Verify template files tồn tại

## 11. Performance

### 11.1 Optimization:
- Events được cache tự động
- Lazy loading cho images
- Minified CSS/JS
- Optimized database queries

### 11.2 Best Practices:
- Sử dụng featured images có kích thước phù hợp
- Giới hạn số events trên archive page
- Regular database cleanup

## Kết luận

Custom Post Type "Sự kiện" đã được tích hợp hoàn chỉnh với theme InThub, cung cấp:
- ✅ Giao diện admin thân thiện
- ✅ Frontend responsive và đẹp mắt  
- ✅ SEO optimization với structured data
- ✅ Tích hợp Google Maps và Calendar
- ✅ Filtering và sorting advanced
- ✅ Mobile-friendly design
- ✅ Performance optimized

Hệ thống sẵn sàng sử dụng ngay và có thể customize dễ dàng theo nhu cầu cụ thể.
