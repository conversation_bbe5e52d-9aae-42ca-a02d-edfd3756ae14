<?php
/**
 * The template for displaying event archives
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container mx-auto px-4 py-16">
        
        <!-- Archive Header -->
        <header class="page-header text-center mb-16">
            <h1 class="page-title text-3xl md:text-4xl font-bold text-blue-900 mb-4">
                <?php esc_html_e('Sự kiện', 'inthub'); ?>
            </h1>
            <div class="archive-description text-lg text-gray-600 max-w-2xl mx-auto">
                <?php esc_html_e('Khám phá các sự kiện thú vị và cơ hội học tập quốc tế', 'inthub'); ?>
            </div>
        </header>

        <!-- Event Filter Tabs -->
        <div class="event-filters mb-12">
            <div class="flex flex-wrap justify-center gap-4">
                <button class="filter-btn active" data-filter="all">
                    <?php esc_html_e('Tất cả', 'inthub'); ?>
                </button>
                <button class="filter-btn" data-filter="upcoming">
                    <?php esc_html_e('Sắp diễn ra', 'inthub'); ?>
                </button>
                <button class="filter-btn" data-filter="ongoing">
                    <?php esc_html_e('Đang diễn ra', 'inthub'); ?>
                </button>
                <button class="filter-btn" data-filter="past">
                    <?php esc_html_e('Đã kết thúc', 'inthub'); ?>
                </button>
            </div>
        </div>

        <!-- Events Grid -->
        <?php if (have_posts()) : ?>
            <div class="events-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php while (have_posts()) : the_post(); ?>
                    <?php
                    // Get event meta data
                    $start_datetime = get_post_meta(get_the_ID(), 'event_start_datetime', true);
                    $end_datetime = get_post_meta(get_the_ID(), 'event_end_datetime', true);
                    $location = get_post_meta(get_the_ID(), 'event_location', true);
                    $short_description = get_post_meta(get_the_ID(), 'event_short_description', true);
                    
                    // Format dates for display
                    $start_date_formatted = $start_datetime ? date('d/m/Y', strtotime($start_datetime)) : '';
                    $start_time_formatted = $start_datetime ? date('H:i', strtotime($start_datetime)) : '';
                    
                    // Determine event status
                    $now = current_time('timestamp');
                    $start_time = $start_datetime ? strtotime($start_datetime) : 0;
                    $end_time = $end_datetime ? strtotime($end_datetime) : 0;
                    
                    $event_status = '';
                    $status_class = '';
                    $filter_class = '';
                    if ($start_time && $end_time) {
                        if ($now < $start_time) {
                            $event_status = 'Sắp diễn ra';
                            $status_class = 'bg-blue-100 text-blue-800';
                            $filter_class = 'upcoming';
                        } elseif ($now >= $start_time && $now <= $end_time) {
                            $event_status = 'Đang diễn ra';
                            $status_class = 'bg-green-100 text-green-800';
                            $filter_class = 'ongoing';
                        } else {
                            $event_status = 'Đã kết thúc';
                            $status_class = 'bg-gray-100 text-gray-800';
                            $filter_class = 'past';
                        }
                    }
                    ?>
                    
                    <article id="post-<?php the_ID(); ?>" <?php post_class('event-card card ' . $filter_class); ?> itemscope itemtype="https://schema.org/Event">
                        
                        <!-- Event Status Badge -->
                        <?php if ($event_status) : ?>
                            <div class="absolute top-4 right-4 z-10">
                                <span class="inline-block px-3 py-1 rounded-full text-xs font-semibold <?php echo $status_class; ?>">
                                    <?php echo $event_status; ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Featured Image -->
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="relative mb-4">
                                <a href="<?php the_permalink(); ?>" class="block">
                                    <?php the_post_thumbnail('medium', array('class' => 'w-full h-48 object-cover rounded-lg', 'itemprop' => 'image')); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Event Header -->
                        <header class="entry-header mb-4">
                            <h2 class="entry-title text-xl font-semibold text-blue-900 mb-3" itemprop="name">
                                <a href="<?php the_permalink(); ?>" class="hover:text-pink-500 transition duration-300">
                                    <?php the_title(); ?>
                                </a>
                            </h2>
                            
                            <!-- Event Meta -->
                            <div class="event-meta space-y-2 text-sm text-gray-600">
                                <?php if ($start_datetime) : ?>
                                    <div class="flex items-center" itemprop="startDate" content="<?php echo esc_attr($start_datetime); ?>">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-pink-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <span><?php echo $start_date_formatted; ?> - <?php echo $start_time_formatted; ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($location) : ?>
                                    <div class="flex items-center" itemprop="location" itemscope itemtype="https://schema.org/Place">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span itemprop="address" title="<?php echo esc_attr($location); ?>">
                                            <?php echo strlen($location) > 40 ? substr($location, 0, 40) . '...' : $location; ?>
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </header>
                        
                        <!-- Event Excerpt -->
                        <div class="entry-summary text-gray-600 mb-4" itemprop="description">
                            <?php if ($short_description) : ?>
                                <?php echo wp_trim_words($short_description, 15); ?>
                            <?php else : ?>
                                <?php echo wp_trim_words(get_the_excerpt(), 15); ?>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Event Footer -->
                        <footer class="entry-footer">
                            <a href="<?php the_permalink(); ?>" class="inline-flex items-center text-pink-500 font-medium hover:text-pink-600 transition duration-300">
                                <?php esc_html_e('Xem chi tiết', 'inthub'); ?>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </footer>
                    </article>
                <?php endwhile; ?>
            </div>
            
            <!-- Pagination -->
            <nav class="pagination-wrapper mt-16" aria-label="<?php esc_attr_e('Events navigation', 'inthub'); ?>">
                <?php
                the_posts_pagination(array(
                    'mid_size'  => 2,
                    'prev_text' => sprintf(
                        '%s <span class="nav-prev-text">%s</span>',
                        '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>',
                        __('Trước', 'inthub')
                    ),
                    'next_text' => sprintf(
                        '<span class="nav-next-text">%s</span> %s',
                        __('Tiếp', 'inthub'),
                        '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>'
                    ),
                ));
                ?>
            </nav>
            
        <?php else : ?>
            
            <!-- No Events Found -->
            <div class="no-events-found text-center py-16">
                <div class="max-w-md mx-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 mx-auto text-gray-400 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    
                    <h2 class="text-2xl font-bold text-blue-900 mb-4">
                        <?php esc_html_e('Chưa có sự kiện nào', 'inthub'); ?>
                    </h2>
                    
                    <p class="text-gray-600 mb-8">
                        <?php esc_html_e('Hiện tại chưa có sự kiện nào được tổ chức. Vui lòng quay lại sau để cập nhật thông tin mới nhất.', 'inthub'); ?>
                    </p>
                    
                    <div class="mt-8">
                        <a href="<?php echo esc_url(home_url('/')); ?>" class="inline-flex items-center justify-center bg-pink-500 text-white font-semibold py-3 px-6 rounded-lg hover:bg-pink-600 transition duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            <?php esc_html_e('Về trang chủ', 'inthub'); ?>
                        </a>
                    </div>
                </div>
            </div>
            
        <?php endif; ?>
    </div>
</main>

<style>
.event-card {
    position: relative;
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.event-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.filter-btn {
    padding: 8px 16px;
    border-radius: 20px;
    border: 2px solid #e5e7eb;
    background: white;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: #ec4899;
    color: white;
    border-color: #ec4899;
}

.event-card.hidden {
    display: none;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const eventCards = document.querySelectorAll('.event-card');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter events
            eventCards.forEach(card => {
                if (filter === 'all') {
                    card.classList.remove('hidden');
                } else {
                    if (card.classList.contains(filter)) {
                        card.classList.remove('hidden');
                    } else {
                        card.classList.add('hidden');
                    }
                }
            });
        });
    });
});
</script>

<?php
get_footer();
?>
